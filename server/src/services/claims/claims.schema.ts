// TypeBox schema for claims service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, queryWrapper, imageSchema, taxSchema, commonPatch } from '../../utils/common/typebox-schemas.js'

// Tax/Fee item schema for pattern properties
const TaxFeeItemSchema = Type.Object({
  name: Type.Optional(Type.String()),
  amount: Type.Optional(Type.Number())
}, { additionalProperties: false })

// Entered by schema
const EnteredBySchema = Type.Object({
  id: Type.Optional(ObjectIdSchema()),
  org: Type.Optional(ObjectIdSchema()),
  auto: Type.Optional(Type.Boolean())
}, { additionalProperties: false })

// Log schema
const LogSchema = Type.Object({
  code: Type.Optional(Type.String()),
  standard: Type.Optional(Type.String())
}, { additionalProperties: false })

// Reduced schema
const ReducedSchema = Type.Object({
  from: Type.Optional(Type.Number()),
  to: Type.Optional(Type.Number()),
  on: Type.Optional(Type.String())
}, { additionalProperties: false })

// Settings schema
const SettingsSchema = Type.Object({
  tax: Type.Optional(taxSchema)
}, { additionalProperties: false })

// Main data model schema
export const claimsSchema = Type.Object({
  _id: ObjectIdSchema(),
  visit: ObjectIdSchema(),
  plan: ObjectIdSchema(),
  patient: ObjectIdSchema(),
  person: ObjectIdSchema(),
  practitioner: Type.Optional(ObjectIdSchema()),
  provider: ObjectIdSchema(),
  procedure: Type.Optional(ObjectIdSchema()),
  med: Type.Optional(ObjectIdSchema()),
  coverage: Type.Optional(ObjectIdSchema()),
  date: Type.Any(),
  misc: Type.Optional(Type.String()),
  enteredBy: Type.Optional(EnteredBySchema),
  log: Type.Optional(LogSchema),
  category: Type.Optional(
    Type.Union([
      Type.Literal("emergency_room"),
      Type.Literal("primary_care"),
      Type.Literal("urgent_care"),
      Type.Literal("dental"),
      Type.Literal("specialist"),
      Type.Literal("mental"),
      Type.Literal("drug"),
    ])
  ),
  description: Type.Optional(Type.String()),
  notes: Type.Optional(Type.String()),
  preventive: Type.Optional(Type.Boolean()),
  amount: Type.Optional(Type.Number()),
  subtotal: Type.Optional(Type.Number()),
  total: Type.Optional(Type.Number()),
  qty: Type.Optional(Type.Number()),
  balanceSyncedAt: Type.Optional(Type.Any()),
  balance: Type.Optional(Type.Number()),
  taxes: Type.Optional(Type.Record(Type.String(), TaxFeeItemSchema)),
  fees: Type.Optional(Type.Record(Type.String(), TaxFeeItemSchema)),
  payments: Type.Optional(Type.Array(ObjectIdSchema())),
  reduced: Type.Optional(ReducedSchema),
  status: Type.Optional(
    Type.Union([
      Type.Literal(0),
      Type.Literal(1),
      Type.Literal(2),
      Type.Literal(3),
      Type.Literal(4),
      Type.Literal(5),
    ])
  ),
  settings: Type.Optional(SettingsSchema),
  files: Type.Optional(Type.Record(Type.String(), imageSchema)),
  threads: Type.Optional(Type.Array(ObjectIdSchema())),
  ...commonFields
}, { $id: 'Claims', additionalProperties: false })

export type Claims = Static<typeof claimsSchema>
export const claimsValidator = getValidator(claimsSchema, dataValidator)
export const claimsResolver = resolve<Claims, HookContext>({})
export const claimsExternalResolver = resolve<Claims, HookContext>({})

// Schema for creating new data
export const claimsDataSchema = Type.Object({
  ...Type.Omit(claimsSchema, ['_id']).properties
}, { additionalProperties: false })

export type ClaimsData = Static<typeof claimsDataSchema>
export const claimsDataValidator = getValidator(claimsDataSchema, dataValidator)
export const claimsDataResolver = resolve<ClaimsData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const claimsQueryProperties = Type.Pick(claimsSchema, ['_id', 'visit', 'plan', 'patient', 'person', 'practitioner', 'provider', 'procedure', 'med', 'coverage', 'payments', 'threads', 'enteredBy', 'createdBy', 'updatedBy'])

export const claimsPatchSchema = commonPatch(claimsSchema, {
  pushPullOpts: [
    { path: 'payments', type: ObjectIdSchema() },
    { path: 'threads', type: ObjectIdSchema() },
    { path: 'files', type: imageSchema }
  ],
  pickedForSet: claimsQueryProperties
})

export type ClaimsPatch = Static<typeof claimsPatchSchema>
export const claimsPatchValidator = getValidator(claimsPatchSchema, dataValidator)
export const claimsPatchResolver = resolve<ClaimsPatch, HookContext>({})

export const claimsQuerySchema = queryWrapper(claimsQueryProperties)

export type ClaimsQuery = Static<typeof claimsQuerySchema>
export const claimsQueryValidator = getValidator(claimsQuerySchema, queryValidator)
export const claimsQueryResolver = resolve<ClaimsQuery, HookContext>({})
